using HolyBless.Buckets.Dtos;
using HolyBless.Configs;
using HolyBless.Entities.Buckets;
using HolyBless.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Buckets
{
    [RemoteService(false)]
    public class StorageBucketAppService : ReadOnlyStorageBucketAppService, IStorageBucketAppService
    {
        private readonly IRepository<BucketToFile> _bucketToFileRepository;

        public StorageBucketAppService(
            IRepository<StorageBucket, int> repository,
            IRepository<BucketToFile> bucketToFileRepository,
            IMemoryCache memoryCache,
            AppConfig settings
        ) : base(repository, memoryCache, settings)
        {
            _bucketToFileRepository = bucketToFileRepository;
        }

        public async Task<StorageBucketDto> GetAsync(int id)
        {
            var bucket = await (await _repository
                .WithDetailsAsync(x => x.StorageProvider))
                .FirstOrDefaultAsync(x => x.Id == id);

            Check.NotNull(bucket, nameof(bucket));
            var bucketDto = ObjectMapper.Map<StorageBucket, StorageBucketDto>(bucket);
            bucketDto.StorageName = bucket.StorageProvider.ProviderName;
            return bucketDto;
        }

        public async Task<List<StorageBucketDto>> GetListByProviderIdAysnc(int providerId)
        {
            return await GetBucketsByProviderIdAsync(providerId);
        }

        public async Task<PagedResultDto<StorageBucketDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var env = _settings.Environment;
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.StorageProvider)
                .Where(x => x.StorageProvider.Environment == env)
                .OrderBy(input.Sorting ?? "BucketName")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var buckets = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<StorageBucketDto>(
                totalCount,
                ObjectMapper.Map<List<StorageBucket>, List<StorageBucketDto>>(buckets)
            );
        }

        [Authorize(HolyBlessPermissions.Buckets.Create)]
        public async Task<StorageBucketDto> CreateAsync(CreateUpdateStorageBucketDto input)
        {
            var bucket = ObjectMapper.Map<CreateUpdateStorageBucketDto, StorageBucket>(input);
            bucket = await _repository.InsertAsync(bucket, autoSave: true);
            
            // Clear cache after creating a storage bucket
            _memoryCache.Remove(KeyBucket);
            
            return ObjectMapper.Map<StorageBucket, StorageBucketDto>(bucket);
        }

        [Authorize(HolyBlessPermissions.Buckets.Edit)]
        public async Task<StorageBucketDto> UpdateAsync(int id, CreateUpdateStorageBucketDto input)
        {
            var bucket = await _repository.GetAsync(id);
            ObjectMapper.Map(input, bucket);
            bucket = await _repository.UpdateAsync(bucket, true);
            
            // Clear cache after updating a storage bucket
            _memoryCache.Remove(KeyBucket);
            
            return ObjectMapper.Map<StorageBucket, StorageBucketDto>(bucket);
        }

        [Authorize(HolyBlessPermissions.Buckets.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
            var links = await _bucketToFileRepository.GetListAsync(x => x.FileId == id);
            if (links.Count > 0)
            {
                await _bucketToFileRepository.DeleteManyAsync(links, true);
            }
            
            // Clear cache after deleting a storage bucket
            _memoryCache.Remove(KeyBucket);
        }
    }
}
