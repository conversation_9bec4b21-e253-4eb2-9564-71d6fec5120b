using AutoMapper;
using HolyBless.Articles;
using HolyBless.Articles.Dtos;
using HolyBless.Books;
using HolyBless.Buckets;
using HolyBless.Buckets.Dtos;
using HolyBless.Channels.Dtos;
using HolyBless.Collections.Dtos;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Books;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Entities.Collections;
using HolyBless.Entities.Tags;
using HolyBless.Lookups;
using HolyBless.Lookups.Dtos;
using HolyBless.StorageProviders;
using HolyBless.Tags.Dtos;
using System.Collections.Generic;
using Volo.Abp.AutoMapper;

namespace HolyBless;

public class HolyBlessApplicationAutoMapperProfile : Profile
{
    public HolyBlessApplicationAutoMapperProfile()
    {
        CreateMap<EBook, EBookDto>();
        CreateMap<CreateUpdateEBookDto, EBook>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ContentCode);

        CreateMap<StorageProvider, StorageProviderDto>();
        CreateMap<CreateUpdateStorageProviderDto, StorageProvider>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.Buckets)
            .Ignore(x => x.Environment)
            .Ignore(x => x.PreferCountries)
            ;

        CreateMap<Bucket, BucketDto>()
            .ForMember(x => x.StorageName, opt => opt.MapFrom(x => x.StorageProvider.ProviderName));

        CreateMap<CreateUpdateBucketDto, Bucket>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.StorageProvider)
            ;
        CreateMap<BucketToFile, BucketToFileDto>()
            .ForMember(x => x.BucketName, opt => opt.MapFrom(x => x.Bucket.BucketName))
            .ForMember(x => x.BucketDomain, opt => opt.MapFrom(x => x.Bucket.SubDomain))
            .ForMember(x => x.FileName, opt => opt.MapFrom(x => x.BucketFile.FileName));

        CreateMap<BucketFile, BucketFileDto>();
        CreateMap<CreateUpdateBucketFileDto, BucketFile>()
            .IgnoreFullAuditedObjectProperties()
            //.Ignore(x => x.BucketToFiles)
            .Ignore(x => x.CollectionToFiles)
            .Ignore(x => x.FolderToBucketFiles)
            .Ignore(x => x.MediaType)
            ;

        CreateMap<Country, CountryDto>();
        CreateMap<CreateUpdateCountryDto, Country>()
            .IgnoreAuditedObjectProperties();

        CreateMap<Collection, CollectionDto>()
            ;
        CreateMap<CreateUpdateCollectionDto, Collection>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ContentCode)
            .Ignore(x => x.ThumbnailBucketFile)
            .Ignore(x => x.CollectionToArticles)
            .Ignore(x => x.CollectionToFiles)
            ;
        CreateMap<CollectionToFile, CollectionToFileDto>();
        CreateMap<CollectionToArticle, CollectionToArticleDto>();

        CreateMap<Channel, ChannelDto>()
            ;
        CreateMap<Channel, ChannelTreeDto>()
            .ForMember(x => x.IsRoot, opt => opt.MapFrom(x => x.ParentChannelId == null))
            .Ignore(x => x.Children);

        CreateMap<CreateUpdateChannelDto, Channel>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ParentChannel)
            .Ignore(x => x.ChildChannels)
            .Ignore(x => x.VirtualDiskFolders)
            ;
        CreateMap<Article, ArticleDto>()
            .ForMember(x => x.ArticleFiles, opt => opt.MapFrom(src =>
                src.ArticleFiles != null ? src.ArticleFiles : new List<ArticleFile>()));
        CreateMap<CreateUpdateArticleDto, Article>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ThumbnailBucketFile)
            .Ignore(x => x.CollectionToArticles)
            .Ignore(x => x.ArticleToTags)
            .Ignore(x => x.ArticleFiles);
        CreateMap<ArticleToTag, ArticleToTagDto>();

        CreateMap<ArticleFile, ArticleFileDto>()
            .ForMember(x => x.ArticleTitle, opt => opt.MapFrom(x => x.Article.Title))
            .ForMember(x => x.FileName, opt => opt.MapFrom(x => x.BucketFile.FileName))
            .ForMember(x => x.MediaType, opt => opt.MapFrom(x => x.BucketFile.MediaType))
            .ForMember(x => x.ContentType, opt => opt.MapFrom(x => x.BucketFile.ContentCategory));

        CreateMap<CreateUpdateArticleFileDto, ArticleFile>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.Article)
            .Ignore(x => x.BucketFile);

        CreateMap<Tag, TagDto>();
        CreateMap<CreateUpdateTagDto, Tag>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ArticleToTags);
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */
    }
}